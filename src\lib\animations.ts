import { animate } from 'animejs';
import { AnimationConfig } from '@/types';

// Default animation configurations
export const defaultAnimations = {
  fadeIn: {
    duration: 1000,
    easing: 'easeOutQuart',
    opacity: [0, 1],
    translateY: [30, 0],
  },
  slideInLeft: {
    duration: 800,
    easing: 'easeOutCubic',
    opacity: [0, 1],
    translateX: [-50, 0],
  },
  slideInRight: {
    duration: 800,
    easing: 'easeOutCubic',
    opacity: [0, 1],
    translateX: [50, 0],
  },
  scaleIn: {
    duration: 600,
    easing: 'easeOutBack',
    opacity: [0, 1],
    scale: [0.8, 1],
  },
  staggerFadeIn: {
    duration: 800,
    easing: 'easeOutQuart',
    opacity: [0, 1],
    translateY: [20, 0],
    delay: (el: any, i: number) => i * 100,
  },
};

// Animation utility functions
export const animateElement = (
  target: string | Element | NodeList,
  config: Partial<AnimationConfig> & Record<string, any>
) => {
  return animate({
    targets: target,
    ...config,
  });
};

export const fadeInOnScroll = (selector: string, options?: Partial<AnimationConfig>) => {
  const elements = document.querySelectorAll(selector);

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          animate({
            targets: entry.target,
            opacity: [0, 1],
            translateY: [30, 0],
            duration: options?.duration || 1000,
            easing: options?.easing || 'easeOutQuart',
            delay: options?.delay || 0,
          });
          observer.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.1 }
  );

  elements.forEach((el) => {
    (el as HTMLElement).style.opacity = '0';
    observer.observe(el);
  });

  return observer;
};

export const staggerAnimation = (
  selector: string,
  animationProps: Record<string, any>,
  staggerDelay: number = 100
) => {
  return animate({
    targets: selector,
    ...animationProps,
    delay: (el: any, i: number) => i * staggerDelay,
  });
};

export const morphPath = (
  target: string | Element,
  newPath: string,
  duration: number = 1000
) => {
  return animate({
    targets: target,
    d: newPath,
    duration,
    easing: 'easeInOutQuart',
  });
};

export const typewriterEffect = (
  target: string | Element,
  text: string,
  duration: number = 2000
) => {
  const element = typeof target === 'string' ? document.querySelector(target) : target;
  if (!element) return;

  element.textContent = '';
  const chars = text.split('');
  let currentIndex = 0;

  return animate({
    targets: { progress: 0 },
    progress: chars.length,
    duration,
    easing: 'linear',
    update: (anim: any) => {
      const newIndex = Math.floor(anim.animatables[0].target.progress);
      if (newIndex !== currentIndex) {
        currentIndex = newIndex;
        element.textContent = chars.slice(0, currentIndex).join('');
      }
    },
  });
};

export const parallaxScroll = (selector: string, speed: number = 0.5) => {
  const elements = document.querySelectorAll(selector);
  
  const handleScroll = () => {
    const scrolled = window.pageYOffset;
    elements.forEach((element) => {
      const rate = scrolled * speed;
      (element as HTMLElement).style.transform = `translateY(${rate}px)`;
    });
  };

  window.addEventListener('scroll', handleScroll);
  return () => window.removeEventListener('scroll', handleScroll);
};

export const hoverAnimation = (
  selector: string,
  hoverProps: Record<string, any>,
  leaveProps: Record<string, any>
) => {
  const elements = document.querySelectorAll(selector);

  elements.forEach((element) => {
    element.addEventListener('mouseenter', () => {
      animate({
        targets: element,
        ...hoverProps,
        duration: 300,
        easing: 'easeOutQuart',
      });
    });

    element.addEventListener('mouseleave', () => {
      animate({
        targets: element,
        ...leaveProps,
        duration: 300,
        easing: 'easeOutQuart',
      });
    });
  });
};

// Loading animation
export const createLoadingAnimation = (target: string | Element) => {
  return animate({
    targets: target,
    rotate: '1turn',
    duration: 1000,
    loop: true,
    easing: 'linear',
  });
};

// Page transition animations
export const pageTransition = {
  enter: (target: string | Element) => {
    return animate({
      targets: target,
      opacity: [0, 1],
      translateY: [20, 0],
      duration: 600,
      easing: 'easeOutQuart',
    });
  },
  exit: (target: string | Element) => {
    return animate({
      targets: target,
      opacity: [1, 0],
      translateY: [0, -20],
      duration: 400,
      easing: 'easeInQuart',
    });
  },
};
