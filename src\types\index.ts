export interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  category: 'motion-graphics' | '3d-animation' | 'video-editing' | 'branding' | 'ui-animation';
  imageUrl: string;
  videoUrl?: string;
  technologies: string[];
  year: number;
  client?: string;
  featured: boolean;
}

export interface Skill {
  name: string;
  level: number; // 1-100
  category: 'software' | 'technique' | 'design';
}

export interface Experience {
  company: string;
  position: string;
  duration: string;
  description: string;
  achievements: string[];
}

export interface ContactInfo {
  email: string;
  phone?: string;
  location: string;
  social: {
    linkedin?: string;
    behance?: string;
    dribbble?: string;
    instagram?: string;
    vimeo?: string;
    youtube?: string;
  };
}

export interface AnimationConfig {
  duration: number;
  delay?: number;
  easing?: string;
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
  loop?: boolean | number;
}
