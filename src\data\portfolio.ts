import { PortfolioItem, Skill, Experience, ContactInfo } from '@/types';

export const portfolioItems: PortfolioItem[] = [
  {
    id: '1',
    title: 'Brand Identity Animation',
    description: 'Dynamic logo animation and brand identity system for a tech startup. Created smooth transitions and engaging micro-interactions.',
    category: 'branding',
    imageUrl: '/portfolio/brand-identity.jpg',
    videoUrl: '/portfolio/brand-identity.mp4',
    technologies: ['After Effects', 'Cinema 4D', 'Illustrator'],
    year: 2024,
    client: 'TechFlow Inc.',
    featured: true,
  },
  {
    id: '2',
    title: 'Product Showcase Video',
    description: 'Sleek 3D product animation showcasing features and benefits with cinematic lighting and smooth camera movements.',
    category: '3d-animation',
    imageUrl: '/portfolio/product-showcase.jpg',
    videoUrl: '/portfolio/product-showcase.mp4',
    technologies: ['Blender', 'After Effects', 'Premiere Pro'],
    year: 2024,
    client: 'InnovateTech',
    featured: true,
  },
  {
    id: '3',
    title: 'UI Animation Package',
    description: 'Complete set of micro-interactions and UI animations for a mobile app, enhancing user experience with delightful motion.',
    category: 'ui-animation',
    imageUrl: '/portfolio/ui-animations.jpg',
    technologies: ['After Effects', 'Lottie', 'Figma'],
    year: 2023,
    client: 'MobileFirst',
    featured: false,
  },
  {
    id: '4',
    title: 'Explainer Video Series',
    description: 'Educational video series with custom illustrations and smooth animations explaining complex concepts in simple terms.',
    category: 'motion-graphics',
    imageUrl: '/portfolio/explainer-video.jpg',
    videoUrl: '/portfolio/explainer-video.mp4',
    technologies: ['After Effects', 'Illustrator', 'Audition'],
    year: 2023,
    client: 'EduTech Solutions',
    featured: true,
  },
  {
    id: '5',
    title: 'Music Video Effects',
    description: 'Creative visual effects and motion graphics for an indie music video, blending live action with animated elements.',
    category: 'video-editing',
    imageUrl: '/portfolio/music-video.jpg',
    videoUrl: '/portfolio/music-video.mp4',
    technologies: ['After Effects', 'Premiere Pro', 'DaVinci Resolve'],
    year: 2023,
    featured: false,
  },
  {
    id: '6',
    title: 'Corporate Presentation',
    description: 'Professional animated presentation with data visualizations and smooth transitions for quarterly business review.',
    category: 'motion-graphics',
    imageUrl: '/portfolio/corporate-presentation.jpg',
    technologies: ['After Effects', 'PowerPoint', 'Illustrator'],
    year: 2022,
    client: 'Global Corp',
    featured: false,
  },
];

export const skills: Skill[] = [
  { name: 'After Effects', level: 95, category: 'software' },
  { name: 'Cinema 4D', level: 88, category: 'software' },
  { name: 'Blender', level: 82, category: 'software' },
  { name: 'Premiere Pro', level: 90, category: 'software' },
  { name: 'Illustrator', level: 85, category: 'software' },
  { name: 'Photoshop', level: 80, category: 'software' },
  { name: 'Motion Design', level: 92, category: 'technique' },
  { name: '3D Animation', level: 85, category: 'technique' },
  { name: 'Character Animation', level: 78, category: 'technique' },
  { name: 'Visual Effects', level: 88, category: 'technique' },
  { name: 'Typography', level: 85, category: 'design' },
  { name: 'Color Theory', level: 90, category: 'design' },
  { name: 'Composition', level: 88, category: 'design' },
];

export const experience: Experience[] = [
  {
    company: 'Creative Studios Inc.',
    position: 'Senior Motion Graphics Designer',
    duration: '2022 - Present',
    description: 'Lead motion graphics projects for major brands and startups, specializing in brand identity animations and explainer videos.',
    achievements: [
      'Increased client satisfaction by 40% through innovative animation techniques',
      'Led a team of 5 designers on high-profile campaigns',
      'Developed company-wide motion design guidelines',
    ],
  },
  {
    company: 'Digital Agency Pro',
    position: 'Motion Graphics Designer',
    duration: '2020 - 2022',
    description: 'Created engaging motion graphics for digital marketing campaigns and social media content.',
    achievements: [
      'Produced over 200 animated assets for various clients',
      'Reduced production time by 30% through workflow optimization',
      'Mentored junior designers in motion graphics techniques',
    ],
  },
  {
    company: 'Freelance',
    position: 'Motion Graphics Designer',
    duration: '2018 - 2020',
    description: 'Provided motion graphics services to small businesses and independent creators.',
    achievements: [
      'Built a client base of 50+ satisfied customers',
      'Specialized in affordable animation solutions for startups',
      'Developed expertise in multiple animation software packages',
    ],
  },
];

export const contactInfo: ContactInfo = {
  email: '<EMAIL>',
  phone: '+****************',
  location: 'New York, NY',
  social: {
    linkedin: 'https://linkedin.com/in/motiondesigner',
    behance: 'https://behance.net/motiondesigner',
    dribbble: 'https://dribbble.com/motiondesigner',
    instagram: 'https://instagram.com/motiondesigner',
    vimeo: 'https://vimeo.com/motiondesigner',
    youtube: 'https://youtube.com/motiondesigner',
  },
};
