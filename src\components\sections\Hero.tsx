'use client';

import { useEffect, useRef } from 'react';
import { ChevronDown, Play, Download } from 'lucide-react';
import { animateElement, typewriterEffect } from '@/lib/animations';

export default function Hero() {
  const heroRef = useRef<HTMLElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    // Animate hero elements on mount
    const timeline = [
      {
        targets: '.hero-title',
        opacity: [0, 1],
        translateY: [50, 0],
        duration: 1000,
        easing: 'easeOutQuart',
      },
      {
        targets: '.hero-subtitle',
        opacity: [0, 1],
        translateY: [30, 0],
        duration: 800,
        delay: 300,
        easing: 'easeOutQuart',
      },
      {
        targets: '.hero-description',
        opacity: [0, 1],
        translateY: [30, 0],
        duration: 800,
        delay: 600,
        easing: 'easeOutQuart',
      },
      {
        targets: '.hero-buttons',
        opacity: [0, 1],
        translateY: [30, 0],
        duration: 800,
        delay: 900,
        easing: 'easeOutQuart',
      },
      {
        targets: '.hero-scroll',
        opacity: [0, 1],
        translateY: [20, 0],
        duration: 600,
        delay: 1200,
        easing: 'easeOutQuart',
      },
    ];

    timeline.forEach((animation) => {
      animateElement(animation.targets, animation);
    });

    // Typewriter effect for the subtitle
    setTimeout(() => {
      typewriterEffect('.typewriter-text', 'Motion Graphics Designer', 2000);
    }, 800);

    // Animated background canvas
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        const particles: Array<{
          x: number;
          y: number;
          vx: number;
          vy: number;
          size: number;
          opacity: number;
        }> = [];

        // Create particles
        for (let i = 0; i < 50; i++) {
          particles.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 0.5,
            vy: (Math.random() - 0.5) * 0.5,
            size: Math.random() * 2 + 1,
            opacity: Math.random() * 0.5 + 0.1,
          });
        }

        const animate = () => {
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          particles.forEach((particle) => {
            particle.x += particle.vx;
            particle.y += particle.vy;

            if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
            if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(59, 130, 246, ${particle.opacity})`;
            ctx.fill();
          });

          requestAnimationFrame(animate);
        };

        animate();

        const handleResize = () => {
          canvas.width = window.innerWidth;
          canvas.height = window.innerHeight;
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
      }
    }
  }, []);

  const scrollToPortfolio = () => {
    const portfolioSection = document.querySelector('#portfolio');
    if (portfolioSection) {
      portfolioSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section
      id="home"
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
    >
      {/* Animated Background Canvas */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0 pointer-events-none opacity-30"
      />

      {/* Background Gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-pink-900/20"></div>
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>

      {/* Content */}
      <div className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-5xl mx-auto">
        {/* Main Title */}
        <h1 className="hero-title opacity-0 text-5xl sm:text-6xl lg:text-7xl font-bold mb-6">
          <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
            Creative
          </span>
          <br />
          <span className="text-white">Motion Designer</span>
        </h1>

        {/* Subtitle with Typewriter Effect */}
        <div className="hero-subtitle opacity-0 text-xl sm:text-2xl lg:text-3xl text-gray-300 mb-6 h-12">
          <span className="typewriter-text"></span>
          <span className="animate-pulse">|</span>
        </div>

        {/* Description */}
        <p className="hero-description opacity-0 text-lg sm:text-xl text-gray-400 mb-8 max-w-3xl mx-auto leading-relaxed">
          Bringing brands to life through stunning motion graphics, 3D animations, and visual effects. 
          I create compelling visual stories that captivate audiences and drive engagement.
        </p>

        {/* Action Buttons */}
        <div className="hero-buttons opacity-0 flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
          <button
            onClick={scrollToPortfolio}
            className="group bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25 flex items-center space-x-2"
          >
            <Play className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
            <span>View My Work</span>
          </button>
          
          <button className="group border-2 border-white/20 hover:border-white/40 text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 hover:bg-white/10 flex items-center space-x-2">
            <Download className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
            <span>Download Resume</span>
          </button>
        </div>

        {/* Scroll Indicator */}
        <div className="hero-scroll opacity-0 absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <button
            onClick={scrollToPortfolio}
            className="flex flex-col items-center space-y-2 text-gray-400 hover:text-white transition-colors duration-300 group"
            aria-label="Scroll to portfolio"
          >
            <span className="text-sm font-medium">Scroll to explore</span>
            <ChevronDown className="w-6 h-6 animate-bounce group-hover:scale-110 transition-transform duration-300" />
          </button>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-4 h-4 bg-blue-500 rounded-full opacity-60 animate-ping"></div>
      <div className="absolute top-40 right-20 w-3 h-3 bg-purple-500 rounded-full opacity-60 animate-ping delay-500"></div>
      <div className="absolute bottom-40 left-20 w-2 h-2 bg-pink-500 rounded-full opacity-60 animate-ping delay-1000"></div>
      <div className="absolute bottom-20 right-10 w-5 h-5 bg-blue-400 rounded-full opacity-40 animate-pulse"></div>
    </section>
  );
}
