import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "MotionDesigner - Creative Motion Graphics Portfolio",
  description: "Professional motion graphics designer specializing in 3D animation, visual effects, and brand identity design. Creating compelling visual stories that captivate audiences.",
  keywords: "motion graphics, 3D animation, visual effects, brand identity, video editing, after effects, cinema 4d",
  authors: [{ name: "MotionDesigner" }],
  viewport: "width=device-width, initial-scale=1",
  openGraph: {
    title: "MotionDesigner - Creative Motion Graphics Portfolio",
    description: "Professional motion graphics designer specializing in 3D animation, visual effects, and brand identity design.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "MotionDesigner - Creative Motion Graphics Portfolio",
    description: "Professional motion graphics designer specializing in 3D animation, visual effects, and brand identity design.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.variable} font-sans antialiased`}>
        {children}
      </body>
    </html>
  );
}
