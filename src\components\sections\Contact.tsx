'use client';

import { useState, useEffect } from 'react';
import { Mail, Phone, MapPin, Send, CheckCircle, AlertCircle } from 'lucide-react';
import { contactInfo } from '@/data/portfolio';
import { animateElement, staggerAnimation } from '@/lib/animations';
import { validateEmail } from '@/lib/utils';

interface FormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

interface FormStatus {
  type: 'idle' | 'loading' | 'success' | 'error';
  message: string;
}

export default function Contact() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    subject: '',
    message: '',
  });
  const [formStatus, setFormStatus] = useState<FormStatus>({
    type: 'idle',
    message: '',
  });

  useEffect(() => {
    // Animate section on scroll
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            animateElement('.contact-title', {
              opacity: [0, 1],
              translateY: [30, 0],
              duration: 800,
              easing: 'easeOutQuart',
            });

            animateElement('.contact-description', {
              opacity: [0, 1],
              translateY: [20, 0],
              duration: 600,
              delay: 200,
              easing: 'easeOutQuart',
            });

            staggerAnimation('.contact-info-item', {
              opacity: [0, 1],
              translateX: [-30, 0],
              duration: 600,
              easing: 'easeOutQuart',
            }, 150);

            animateElement('.contact-form', {
              opacity: [0, 1],
              translateY: [30, 0],
              duration: 800,
              delay: 400,
              easing: 'easeOutQuart',
            });

            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    const contactSection = document.querySelector('#contact');
    if (contactSection) {
      observer.observe(contactSection);
    }

    return () => observer.disconnect();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.name.trim() || !formData.email.trim() || !formData.message.trim()) {
      setFormStatus({
        type: 'error',
        message: 'Please fill in all required fields.',
      });
      return;
    }

    if (!validateEmail(formData.email)) {
      setFormStatus({
        type: 'error',
        message: 'Please enter a valid email address.',
      });
      return;
    }

    setFormStatus({ type: 'loading', message: 'Sending message...' });

    // Simulate form submission
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setFormStatus({
        type: 'success',
        message: 'Thank you! Your message has been sent successfully.',
      });
      
      setFormData({ name: '', email: '', subject: '', message: '' });
      
      // Animate success
      animateElement('.form-success', {
        scale: [0.8, 1],
        opacity: [0, 1],
        duration: 600,
        easing: 'easeOutBack',
      });
    } catch (error) {
      setFormStatus({
        type: 'error',
        message: 'Sorry, there was an error sending your message. Please try again.',
      });
    }
  };

  const contactItems = [
    {
      icon: Mail,
      label: 'Email',
      value: contactInfo.email,
      href: `mailto:${contactInfo.email}`,
    },
    {
      icon: Phone,
      label: 'Phone',
      value: contactInfo.phone || 'Available on request',
      href: contactInfo.phone ? `tel:${contactInfo.phone}` : undefined,
    },
    {
      icon: MapPin,
      label: 'Location',
      value: contactInfo.location,
      href: undefined,
    },
  ];

  return (
    <section id="contact" className="py-20 bg-gradient-to-b from-gray-900 to-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="contact-title opacity-0 text-4xl sm:text-5xl font-bold mb-6">
            <span className="text-white">Get In</span>
            <span className="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent"> Touch</span>
          </h2>
          <p className="contact-description opacity-0 text-xl text-gray-400 max-w-3xl mx-auto">
            Ready to bring your vision to life? Let&apos;s discuss your next motion graphics project
            and create something extraordinary together.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Contact Information */}
          <div>
            <h3 className="text-2xl font-bold text-white mb-8">Let&apos;s Connect</h3>
            
            <div className="space-y-6 mb-8">
              {contactItems.map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <div
                    key={item.label}
                    className="contact-info-item opacity-0 flex items-center space-x-4 p-4 bg-gray-800/50 rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 group"
                  >
                    <div className="bg-gradient-to-br from-blue-500/20 to-purple-500/20 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300">
                      <IconComponent className="w-6 h-6 text-blue-400" />
                    </div>
                    <div>
                      <div className="text-gray-400 text-sm">{item.label}</div>
                      {item.href ? (
                        <a
                          href={item.href}
                          className="text-white hover:text-blue-400 transition-colors duration-300"
                        >
                          {item.value}
                        </a>
                      ) : (
                        <div className="text-white">{item.value}</div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Social Links */}
            <div>
              <h4 className="text-lg font-semibold text-white mb-4">Follow Me</h4>
              <div className="flex space-x-4">
                {Object.entries(contactInfo.social).map(([platform, url]) => {
                  if (!url) return null;
                  return (
                    <a
                      key={platform}
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-gray-800/50 hover:bg-gray-700/50 p-3 rounded-lg border border-white/10 hover:border-white/20 transition-all duration-300 transform hover:scale-110 hover:rotate-12"
                    >
                      <span className="text-blue-400 capitalize">{platform}</span>
                    </a>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="contact-form opacity-0">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                    Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-300"
                    placeholder="Your name"
                  />
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-300"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-300 mb-2">
                  Subject
                </label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-300"
                  placeholder="Project inquiry"
                />
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                  Message *
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  rows={6}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-300 resize-none"
                  placeholder="Tell me about your project..."
                />
              </div>

              {/* Form Status */}
              {formStatus.type !== 'idle' && (
                <div
                  className={`form-status p-4 rounded-lg flex items-center space-x-3 ${
                    formStatus.type === 'success'
                      ? 'bg-green-500/20 border border-green-500/30 text-green-400'
                      : formStatus.type === 'error'
                      ? 'bg-red-500/20 border border-red-500/30 text-red-400'
                      : 'bg-blue-500/20 border border-blue-500/30 text-blue-400'
                  }`}
                >
                  {formStatus.type === 'success' && <CheckCircle className="w-5 h-5" />}
                  {formStatus.type === 'error' && <AlertCircle className="w-5 h-5" />}
                  {formStatus.type === 'loading' && (
                    <div className="w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
                  )}
                  <span>{formStatus.message}</span>
                </div>
              )}

              <button
                type="submit"
                disabled={formStatus.type === 'loading'}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-700 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25 disabled:scale-100 disabled:shadow-none flex items-center justify-center space-x-2"
              >
                {formStatus.type === 'loading' ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Sending...</span>
                  </>
                ) : (
                  <>
                    <Send className="w-5 h-5" />
                    <span>Send Message</span>
                  </>
                )}
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
}
