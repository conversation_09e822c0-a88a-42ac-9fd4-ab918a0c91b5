'use client';

import { useEffect, useState } from 'react';
import { Award, Users, Coffee, Zap } from 'lucide-react';
import { skills, experience } from '@/data/portfolio';
import { animateElement, staggerAnimation } from '@/lib/animations';
import { cn } from '@/lib/utils';

const stats = [
  { icon: Award, label: 'Projects Completed', value: '150+' },
  { icon: Users, label: 'Happy Clients', value: '50+' },
  { icon: Coffee, label: 'Cups of Coffee', value: '1000+' },
  { icon: Zap, label: 'Years Experience', value: '6+' },
];

export default function About() {
  const [animatedSkills, setAnimatedSkills] = useState<{ [key: string]: number }>({});

  useEffect(() => {
    // Animate section on scroll
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Animate title and description
            animateElement('.about-title', {
              opacity: [0, 1],
              translateY: [30, 0],
              duration: 800,
              easing: 'easeOutQuart',
            });

            animateElement('.about-description', {
              opacity: [0, 1],
              translateY: [20, 0],
              duration: 600,
              delay: 200,
              easing: 'easeOutQuart',
            });

            // Animate stats
            staggerAnimation('.stat-item', {
              opacity: [0, 1],
              translateY: [30, 0],
              scale: [0.8, 1],
              duration: 600,
              easing: 'easeOutBack',
            }, 100);

            // Animate skills
            setTimeout(() => {
              animateElement('.skills-section', {
                opacity: [0, 1],
                translateY: [30, 0],
                duration: 800,
                easing: 'easeOutQuart',
              });

              // Animate skill bars
              skills.forEach((skill, index) => {
                setTimeout(() => {
                  setAnimatedSkills(prev => ({ ...prev, [skill.name]: skill.level }));
                }, index * 100);
              });
            }, 600);

            // Animate experience
            setTimeout(() => {
              staggerAnimation('.experience-item', {
                opacity: [0, 1],
                translateX: [-30, 0],
                duration: 600,
                easing: 'easeOutQuart',
              }, 150);
            }, 1000);

            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    const aboutSection = document.querySelector('#about');
    if (aboutSection) {
      observer.observe(aboutSection);
    }

    return () => observer.disconnect();
  }, []);

  const groupedSkills = skills.reduce((acc, skill) => {
    if (!acc[skill.category]) {
      acc[skill.category] = [];
    }
    acc[skill.category].push(skill);
    return acc;
  }, {} as { [key: string]: typeof skills });

  return (
    <section id="about" className="py-20 bg-gradient-to-b from-black to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="about-title opacity-0 text-4xl sm:text-5xl font-bold mb-6">
            <span className="text-white">About</span>
            <span className="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent"> Me</span>
          </h2>
          <p className="about-description opacity-0 text-xl text-gray-400 max-w-3xl mx-auto">
            Passionate motion graphics designer with 6+ years of experience creating compelling visual stories 
            that captivate audiences and drive engagement across various industries.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <div
                key={stat.label}
                className="stat-item opacity-0 text-center group"
              >
                <div className="bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 transform hover:scale-105">
                  <IconComponent className="w-8 h-8 text-blue-400 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300" />
                  <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                  <div className="text-gray-400 text-sm">{stat.label}</div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Skills Section */}
          <div className="skills-section opacity-0">
            <h3 className="text-2xl font-bold text-white mb-8">Skills & Expertise</h3>
            
            {Object.entries(groupedSkills).map(([category, categorySkills]) => (
              <div key={category} className="mb-8">
                <h4 className="text-lg font-semibold text-blue-400 mb-4 capitalize">
                  {category.replace('-', ' ')}
                </h4>
                <div className="space-y-4">
                  {categorySkills.map((skill) => (
                    <div key={skill.name} className="skill-item">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-gray-300 font-medium">{skill.name}</span>
                        <span className="text-gray-400 text-sm">{skill.level}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2 overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-1000 ease-out"
                          style={{
                            width: `${animatedSkills[skill.name] || 0}%`,
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Experience Section */}
          <div>
            <h3 className="text-2xl font-bold text-white mb-8">Experience</h3>
            <div className="space-y-8">
              {experience.map((exp, index) => (
                <div
                  key={index}
                  className="experience-item opacity-0 relative pl-8 border-l-2 border-blue-500/30"
                >
                  <div className="absolute -left-2 top-0 w-4 h-4 bg-blue-500 rounded-full"></div>
                  
                  <div className="bg-gray-800/50 rounded-xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                      <h4 className="text-xl font-semibold text-white">{exp.position}</h4>
                      <span className="text-blue-400 text-sm font-medium">{exp.duration}</span>
                    </div>
                    
                    <h5 className="text-lg text-gray-300 mb-3">{exp.company}</h5>
                    
                    <p className="text-gray-400 mb-4">{exp.description}</p>
                    
                    <ul className="space-y-2">
                      {exp.achievements.map((achievement, achievementIndex) => (
                        <li
                          key={achievementIndex}
                          className="text-gray-400 text-sm flex items-start"
                        >
                          <span className="text-blue-400 mr-2 mt-1">•</span>
                          {achievement}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Personal Touch */}
        <div className="mt-20 text-center">
          <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl p-8 border border-white/10">
            <h3 className="text-2xl font-bold text-white mb-4">Let's Create Something Amazing</h3>
            <p className="text-gray-400 mb-6 max-w-2xl mx-auto">
              I&apos;m always excited to work on new projects and collaborate with creative minds.
              Whether you need motion graphics, 3D animations, or visual effects, let&apos;s bring your vision to life.
            </p>
            <button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25">
              Start a Project
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
