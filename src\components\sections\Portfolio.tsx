'use client';

import { useState, useEffect } from 'react';
import { Filter, ExternalLink, Play } from 'lucide-react';
import { portfolioItems } from '@/data/portfolio';
import { animateElement, staggerAnimation } from '@/lib/animations';
import { cn } from '@/lib/utils';
import type { PortfolioItem } from '@/types';

const categories = [
  { id: 'all', name: 'All Work' },
  { id: 'motion-graphics', name: 'Motion Graphics' },
  { id: '3d-animation', name: '3D Animation' },
  { id: 'branding', name: 'Branding' },
  { id: 'ui-animation', name: 'UI Animation' },
  { id: 'video-editing', name: 'Video Editing' },
];

export default function Portfolio() {
  const [activeCategory, setActiveCategory] = useState('all');
  const [filteredItems, setFilteredItems] = useState<PortfolioItem[]>(portfolioItems);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Animate section on scroll
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            animateElement('.portfolio-title', {
              opacity: [0, 1],
              translateY: [30, 0],
              duration: 800,
              easing: 'easeOutQuart',
            });

            animateElement('.portfolio-filters', {
              opacity: [0, 1],
              translateY: [20, 0],
              duration: 600,
              delay: 200,
              easing: 'easeOutQuart',
            });

            staggerAnimation('.portfolio-item', {
              opacity: [0, 1],
              translateY: [30, 0],
              scale: [0.9, 1],
              duration: 600,
              easing: 'easeOutQuart',
            }, 100);

            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    const portfolioSection = document.querySelector('#portfolio');
    if (portfolioSection) {
      observer.observe(portfolioSection);
    }

    return () => observer.disconnect();
  }, []);

  const handleCategoryChange = (categoryId: string) => {
    if (categoryId === activeCategory) return;

    setIsLoading(true);
    setActiveCategory(categoryId);

    // Animate out current items
    animateElement('.portfolio-item', {
      opacity: [1, 0],
      scale: [1, 0.8],
      duration: 300,
      easing: 'easeInQuart',
      complete: () => {
        // Filter items
        const filtered = categoryId === 'all' 
          ? portfolioItems 
          : portfolioItems.filter(item => item.category === categoryId);
        
        setFilteredItems(filtered);
        
        // Animate in new items
        setTimeout(() => {
          staggerAnimation('.portfolio-item', {
            opacity: [0, 1],
            scale: [0.8, 1],
            translateY: [20, 0],
            duration: 400,
            easing: 'easeOutQuart',
          }, 80);
          setIsLoading(false);
        }, 100);
      },
    });
  };

  return (
    <section id="portfolio" className="py-20 bg-gradient-to-b from-gray-900 to-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="portfolio-title opacity-0 text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
              Featured
            </span>
            <span className="text-white"> Work</span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            A showcase of my latest motion graphics projects, 3D animations, and visual effects work
          </p>
        </div>

        {/* Category Filters */}
        <div className="portfolio-filters opacity-0 flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleCategoryChange(category.id)}
              className={cn(
                'px-6 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105',
                activeCategory === category.id
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg shadow-purple-500/25'
                  : 'bg-white/10 text-gray-300 hover:bg-white/20 hover:text-white'
              )}
            >
              <Filter className="w-4 h-4 inline-block mr-2" />
              {category.name}
            </button>
          ))}
        </div>

        {/* Portfolio Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredItems.map((item, index) => (
            <PortfolioCard key={item.id} item={item} index={index} />
          ))}
        </div>

        {/* Load More Button */}
        {filteredItems.length > 6 && (
          <div className="text-center mt-12">
            <button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25">
              Load More Projects
            </button>
          </div>
        )}
      </div>
    </section>
  );
}

interface PortfolioCardProps {
  item: PortfolioItem;
  index: number;
}

function PortfolioCard({ item, index }: PortfolioCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const cardElement = document.querySelector(`.portfolio-item-${item.id}`);
    if (cardElement) {
      const handleMouseEnter = () => {
        setIsHovered(true);
        animateElement(`.portfolio-item-${item.id} .card-overlay`, {
          opacity: [0, 1],
          duration: 300,
          easing: 'easeOutQuart',
        });

        animateElement(`.portfolio-item-${item.id} .card-content`, {
          translateY: [20, 0],
          opacity: [0, 1],
          duration: 400,
          delay: 100,
          easing: 'easeOutQuart',
        });
      };

      const handleMouseLeave = () => {
        setIsHovered(false);
        animateElement(`.portfolio-item-${item.id} .card-overlay`, {
          opacity: [1, 0],
          duration: 200,
          easing: 'easeInQuart',
        });
      };

      cardElement.addEventListener('mouseenter', handleMouseEnter);
      cardElement.addEventListener('mouseleave', handleMouseLeave);

      return () => {
        cardElement.removeEventListener('mouseenter', handleMouseEnter);
        cardElement.removeEventListener('mouseleave', handleMouseLeave);
      };
    }
  }, [item.id]);

  return (
    <div
      className={`portfolio-item portfolio-item-${item.id} opacity-0 group relative bg-gray-800/50 rounded-2xl overflow-hidden backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/10`}
    >
      {/* Image */}
      <div className="relative aspect-video overflow-hidden">
        <div className="w-full h-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
          <div className="text-6xl opacity-20">🎬</div>
        </div>

        {/* Overlay */}
        <div className="card-overlay absolute inset-0 bg-black/60 opacity-0 flex items-center justify-center">
          <div className="card-content opacity-0 flex space-x-4">
            {item.videoUrl && (
              <button className="bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 transform hover:scale-110">
                <Play className="w-6 h-6" />
              </button>
            )}
            <button className="bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 transform hover:scale-110">
              <ExternalLink className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Featured Badge */}
        {item.featured && (
          <div className="absolute top-4 left-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-3 py-1 rounded-full text-sm font-semibold">
            Featured
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-blue-400 font-medium capitalize">
            {item.category.replace('-', ' ')}
          </span>
          <span className="text-sm text-gray-500">{item.year}</span>
        </div>

        <h3 className="text-xl font-bold text-white mb-2 group-hover:text-blue-400 transition-colors duration-300">
          {item.title}
        </h3>

        <p className="text-gray-400 text-sm mb-4 line-clamp-2">
          {item.description}
        </p>

        {/* Technologies */}
        <div className="flex flex-wrap gap-2 mb-4">
          {item.technologies.slice(0, 3).map((tech) => (
            <span
              key={tech}
              className="bg-white/10 text-gray-300 px-2 py-1 rounded-md text-xs"
            >
              {tech}
            </span>
          ))}
          {item.technologies.length > 3 && (
            <span className="text-gray-500 text-xs">+{item.technologies.length - 3} more</span>
          )}
        </div>

        {/* Client */}
        {item.client && (
          <div className="text-xs text-gray-500">
            Client: <span className="text-gray-400">{item.client}</span>
          </div>
        )}
      </div>
    </div>
  );
}
