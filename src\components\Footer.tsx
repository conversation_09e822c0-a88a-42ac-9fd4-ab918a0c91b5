'use client';

import { useEffect } from 'react';
import { Github, Linkedin, Mail, ExternalLink } from 'lucide-react';
import { contactInfo } from '@/data/portfolio';
import { animateElement, staggerAnimation } from '@/lib/animations';

const socialIcons = {
  linkedin: Linkedin,
  github: Github,
  email: Mail,
};

export default function Footer() {
  useEffect(() => {
    // Animate footer elements when they come into view
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            animateElement('.footer-content', {
              opacity: [0, 1],
              translateY: [30, 0],
              duration: 800,
              easing: 'easeOutQuart',
            });

            staggerAnimation('.footer-social', {
              opacity: [0, 1],
              scale: [0.8, 1],
              duration: 600,
              easing: 'easeOutBack',
            }, 100);

            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    const footerElement = document.querySelector('footer');
    if (footerElement) {
      observer.observe(footerElement);
    }

    return () => observer.disconnect();
  }, []);

  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-black/50 backdrop-blur-md border-t border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="footer-content opacity-0">
          {/* Main Footer Content */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            {/* Brand Section */}
            <div className="space-y-4">
              <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                MotionDesigner
              </h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Creating stunning motion graphics and animations that bring brands to life. 
                Specializing in 3D animation, visual effects, and brand identity design.
              </p>
            </div>

            {/* Quick Links */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-white">Quick Links</h4>
              <ul className="space-y-2">
                {['Home', 'Portfolio', 'About', 'Contact'].map((link) => (
                  <li key={link}>
                    <a
                      href={`#${link.toLowerCase()}`}
                      className="text-gray-400 hover:text-white transition-colors duration-300 text-sm flex items-center group"
                    >
                      <span className="group-hover:translate-x-1 transition-transform duration-300">
                        {link}
                      </span>
                      <ExternalLink className="w-3 h-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Info */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-white">Get In Touch</h4>
              <div className="space-y-2 text-sm">
                <p className="text-gray-400">
                  <span className="text-white">Email:</span> {contactInfo.email}
                </p>
                <p className="text-gray-400">
                  <span className="text-white">Location:</span> {contactInfo.location}
                </p>
                {contactInfo.phone && (
                  <p className="text-gray-400">
                    <span className="text-white">Phone:</span> {contactInfo.phone}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Social Links */}
          <div className="flex flex-col sm:flex-row items-center justify-between pt-8 border-t border-white/10">
            <div className="flex space-x-6 mb-4 sm:mb-0">
              {Object.entries(contactInfo.social).map(([platform, url], index) => {
                if (!url) return null;
                
                const IconComponent = platform === 'linkedin' ? Linkedin : 
                                    platform === 'github' ? Github : Mail;
                
                return (
                  <a
                    key={platform}
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="footer-social opacity-0 text-gray-400 hover:text-white transition-all duration-300 hover:scale-110 hover:rotate-12 p-2 rounded-lg hover:bg-white/10"
                    aria-label={`Visit ${platform} profile`}
                  >
                    <IconComponent className="w-5 h-5" />
                  </a>
                );
              })}
            </div>

            {/* Copyright */}
            <div className="text-gray-400 text-sm text-center sm:text-right">
              <p>© {currentYear} MotionDesigner. All rights reserved.</p>
              <p className="mt-1">
                Built with{' '}
                <span className="text-red-400 animate-pulse">♥</span>{' '}
                using Next.js & Anime.js
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-purple-500/10 to-pink-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>
    </footer>
  );
}
