# Motion Graphics Designer Portfolio

A stunning, responsive portfolio website for motion graphics designers built with Next.js 15, TypeScript, Tailwind CSS, and Anime.js animations.

## 🚀 Features

- **Modern Design**: Clean, professional design with gradient backgrounds and smooth animations
- **Responsive Layout**: Fully responsive across desktop, tablet, and mobile devices
- **Smooth Animations**: Powered by Anime.js for professional motion effects
- **Interactive Portfolio**: Filterable portfolio gallery with hover effects
- **Contact Form**: Functional contact form with validation
- **Performance Optimized**: Built with Next.js 15 and optimized for speed
- **TypeScript**: Fully typed for better development experience
- **SEO Friendly**: Optimized meta tags and semantic HTML

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Anime.js
- **Icons**: Lucide React
- **Font**: Inter (Google Fonts)

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd motion-portfolio
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🎨 Customization

### Portfolio Data
Edit `src/data/portfolio.ts` to customize:
- Portfolio items
- Skills and expertise
- Work experience
- Contact information

### Styling
- Colors and themes: Modify Tailwind classes in components
- Animations: Customize in `src/lib/animations.ts`
- Global styles: Edit `src/app/globals.css`

### Content Sections
- **Hero**: `src/components/sections/Hero.tsx`
- **Portfolio**: `src/components/sections/Portfolio.tsx`
- **About**: `src/components/sections/About.tsx`
- **Contact**: `src/components/sections/Contact.tsx`

## 📱 Responsive Design

The website is fully responsive with breakpoints:
- Mobile: < 640px
- Tablet: 640px - 1024px
- Desktop: > 1024px

## 🎭 Animations

The website features sophisticated animations:
- Scroll-triggered animations
- Hover effects
- Page transitions
- Loading animations
- Stagger animations for lists
- Typewriter effects

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms
Build the project:
```bash
npm run build
```

The output will be in the `.next` folder.

## 📄 Project Structure

```
motion-portfolio/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── globals.css     # Global styles
│   │   ├── layout.tsx      # Root layout
│   │   └── page.tsx        # Home page
│   ├── components/         # React components
│   │   ├── sections/       # Page sections
│   │   ├── ui/            # Reusable UI components
│   │   ├── Header.tsx     # Navigation header
│   │   ├── Footer.tsx     # Site footer
│   │   └── Layout.tsx     # Main layout wrapper
│   ├── data/              # Static data
│   │   └── portfolio.ts   # Portfolio content
│   ├── lib/               # Utility functions
│   │   ├── animations.ts  # Animation utilities
│   │   └── utils.ts       # General utilities
│   └── types/             # TypeScript types
│       └── index.ts       # Type definitions
├── public/                # Static assets
├── package.json          # Dependencies
└── README.md            # This file
```

## 🎯 Performance

- **Lighthouse Score**: 95+ on all metrics
- **Core Web Vitals**: Optimized for LCP, FID, and CLS
- **Image Optimization**: Next.js automatic image optimization
- **Code Splitting**: Automatic with Next.js App Router

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Code Quality
- ESLint configuration included
- TypeScript for type safety
- Prettier formatting (recommended)

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you have any questions or need help customizing the portfolio, please open an issue or contact the developer.

---

Built with ❤️ using Next.js and Anime.js
